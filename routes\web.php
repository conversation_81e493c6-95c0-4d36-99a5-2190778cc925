<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\MenuItemController;
use App\Http\Controllers\Admin\NewsController as AdminNewsController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\Admin\FontSettingController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');



// Public Pages
Route::get('/menu', [MenuController::class, 'index'])->name('menu.index');
Route::get('/menu/{id}', [MenuController::class, 'show'])->name('menu.show');
Route::get('/menu/category/{slug}', [MenuController::class, 'category'])->name('menu.category');
Route::get('/menu/{id}', [MenuController::class, 'show'])->name('menu.show');
Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{id}', [NewsController::class, 'show'])->name('news.show');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [ContactController::class, 'index'])->name('contact.index');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Test Login Route
Route::get('/test-login', function () {
    return view('test-login');
})->name('test.login');
Route::post('/test-login', [AuthController::class, 'login'])->name('test.login.post');

Route::get('/test-admin', function () {
    return view('test-admin');
})->name('test.admin');

// Debug Routes
Route::get('/debug-auth', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user);
        return redirect()->route('admin.dashboard')->with('success', 'Debug login successful');
    }
    return 'Admin user not found';
})->name('debug.auth');

// Force login route
Route::get('/force-login', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();
        return redirect('/admin')->with('success', 'Force login successful');
    }
    return 'Admin user not found';
})->name('force.login');

// Direct admin dashboard access
Route::get('/direct-admin', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();

        // Prepare stats data
        $stats = [
            'categories' => \App\Models\Category::count(),
            'menu_items' => \App\Models\MenuItem::count(),
            'news' => \App\Models\News::count(),
        ];

        return view('admin.simple-dashboard', compact('stats'));
    }
    return 'Admin user not found';
})->name('direct.admin');

// Direct admin access for testing
Route::get('/test-admin-direct', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();
        return view('admin.dashboard');
    }
    return 'Admin user not found';
})->name('test.admin.direct');

// Simple login form
Route::get('/simple-login', function () {
    return view('auth.simple-login');
})->name('simple.login');

Route::post('/simple-login', function (\Illuminate\Http\Request $request) {
    $credentials = $request->only('email', 'password');

    if (\Illuminate\Support\Facades\Auth::attempt($credentials, true)) {
        $request->session()->regenerate();

        if (\Illuminate\Support\Facades\Auth::user()->isAdmin()) {
            return redirect('/admin')->with('success', 'เข้าสู่ระบบสำเร็จ');
        }

        return redirect('/')->with('success', 'เข้าสู่ระบบสำเร็จ');
    }

    return back()->withErrors(['email' => 'อีเมลหรือรหัสผ่านไม่ถูกต้อง']);
})->name('simple.login.post');

Route::get('/debug-session', function () {
    return [
        'session_id' => session()->getId(),
        'auth_check' => \Illuminate\Support\Facades\Auth::check(),
        'user' => \Illuminate\Support\Facades\Auth::user(),
        'session_data' => session()->all(),
    ];
})->name('debug.session');

// Simple Login Test
Route::get('/simple-login', function () {
    return view('simple-login');
})->name('simple.login');

Route::post('/simple-login', [AuthController::class, 'login'])->name('simple.login.post');

// Admin Routes
Route::prefix('admin')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('admin.dashboard');

    // Category Management
    Route::resource('categories', CategoryController::class)->names([
        'index' => 'admin.categories.index',
        'create' => 'admin.categories.create',
        'store' => 'admin.categories.store',
        'show' => 'admin.categories.show',
        'edit' => 'admin.categories.edit',
        'update' => 'admin.categories.update',
        'destroy' => 'admin.categories.destroy',
    ]);

    // Menu Item Management
    Route::resource('menu-items', MenuItemController::class)->names([
        'index' => 'admin.menu-items.index',
        'create' => 'admin.menu-items.create',
        'store' => 'admin.menu-items.store',
        'show' => 'admin.menu-items.show',
        'edit' => 'admin.menu-items.edit',
        'update' => 'admin.menu-items.update',
        'destroy' => 'admin.menu-items.destroy',
    ]);

    // News Management
    Route::resource('news', AdminNewsController::class)->names([
        'index' => 'admin.news.index',
        'create' => 'admin.news.create',
        'store' => 'admin.news.store',
        'show' => 'admin.news.show',
        'edit' => 'admin.news.edit',
        'update' => 'admin.news.update',
        'destroy' => 'admin.news.destroy',
    ]);

    // User Management - ลบออกแล้ว

    // Restaurant Info Management
    Route::get('restaurant-info', [App\Http\Controllers\Admin\RestaurantInfoController::class, 'index'])->name('admin.restaurant-info.index');
    Route::get('restaurant-info/edit', [App\Http\Controllers\Admin\RestaurantInfoController::class, 'edit'])->name('admin.restaurant-info.edit');
    Route::put('restaurant-info', [App\Http\Controllers\Admin\RestaurantInfoController::class, 'update'])->name('admin.restaurant-info.update');

    // Hero Content Management
    Route::get('hero-content', [App\Http\Controllers\Admin\HeroContentController::class, 'index'])->name('admin.hero-content.index');
    Route::get('hero-content/edit', [App\Http\Controllers\Admin\HeroContentController::class, 'edit'])->name('admin.hero-content.edit');
    Route::put('hero-content', [App\Http\Controllers\Admin\HeroContentController::class, 'update'])->name('admin.hero-content.update');

    // About Page Management
    Route::get('about-page', [App\Http\Controllers\Admin\AboutPageController::class, 'index'])->name('admin.about-page.index');
    Route::get('about-page/edit', [App\Http\Controllers\Admin\AboutPageController::class, 'edit'])->name('admin.about-page.edit');
    Route::put('about-page', [App\Http\Controllers\Admin\AboutPageController::class, 'update'])->name('admin.about-page.update');

    // Contact Page Management
    Route::get('contact-page', [App\Http\Controllers\Admin\ContactPageController::class, 'index'])->name('admin.contact-page.index');
    Route::get('contact-page/edit', [App\Http\Controllers\Admin\ContactPageController::class, 'edit'])->name('admin.contact-page.edit');
    Route::put('contact-page', [App\Http\Controllers\Admin\ContactPageController::class, 'update'])->name('admin.contact-page.update');

    // Image Management API
    Route::get('images/api', [App\Http\Controllers\Admin\ImageController::class, 'api'])->name('admin.images.api');
    Route::post('images/upload', [App\Http\Controllers\Admin\ImageController::class, 'upload'])->name('admin.images.upload');
});
