@extends('layouts.admin')

@section('title', 'จัดการเมนูอาหาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่หน้าหลัก
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <span class="icon-boat-noodle me-2"></span>จัดการเมนูอาหาร
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active">เมนูอาหาร</li>
                        </ol>
                    </nav>
                </div>
                <a href="{{ route('admin.menu-items.create') }}" class="btn btn-primary btn-add">
                    <i class="fas fa-plus me-2"></i>เพิ่มเมนูใหม่
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.menu-items.index') }}">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">ค้นหาเมนู</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="search" 
                                       name="search" 
                                       value="{{ request('search') }}" 
                                       placeholder="ชื่อเมนูอาหาร">
                            </div>
                            <div class="col-md-4">
                                <label for="category" class="form-label">หมวดหมู่</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">ทุกหมวดหมู่</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" 
                                                {{ request('category') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>ค้นหา
                                    </button>
                                    <a href="{{ route('admin.menu-items.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>ล้าง
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Items Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>รายการเมนูอาหาร
                        <span class="badge bg-primary ms-2">{{ $menuItems->total() }} รายการ</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if($menuItems->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 80px;">รูปภาพ</th>
                                        <th>ชื่อเมนู</th>
                                        <th>หมวดหมู่</th>
                                        <th style="width: 100px;">ราคา</th>
                                        <th style="width: 100px;">ลำดับ</th>
                                        <th style="width: 120px;">สถานะ</th>
                                        <th style="width: 150px;">การจัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($menuItems as $item)
                                        <tr>
                                            <td>{{ $item->id }}</td>
                                            <td>
                                                @if($item->image)
                                                    <img src="{{ asset('storage/' . $item->image) }}"
                                                         alt="{{ $item->name }}"
                                                         class="rounded"
                                                         style="width: 50px; height: 50px; object-fit: cover;"
                                                         loading="lazy"
                                                         onerror="this.onerror=null; this.src='{{ asset('images/menu/placeholder.svg') }}'; this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px; display: none;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                @else
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ $item->name }}</strong>
                                                    @if($item->is_featured)
                                                        <span class="badge bg-warning text-dark ms-1">แนะนำ</span>
                                                    @endif
                                                </div>
                                                @if($item->description)
                                                    <small class="text-muted">{{ Str::limit($item->description, 50) }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ $item->category->name }}</span>
                                            </td>
                                            <td>
                                                <strong class="text-primary">{{ $item->formatted_price }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $item->sort_order }}</span>
                                            </td>
                                            <td>
                                                @if($item->is_active)
                                                    <span class="badge bg-success">เปิดใช้งาน</span>
                                                @else
                                                    <span class="badge bg-danger">ปิดใช้งาน</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.menu-items.show', $item) }}" 
                                                       class="btn btn-sm btn-outline-info" 
                                                       title="ดูรายละเอียด">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.menu-items.edit', $item) }}" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="แก้ไข">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="ลบ"
                                                            onclick="confirmDelete({{ $item->id }}, '{{ $item->name }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                
                                                <!-- Hidden Delete Form -->
                                                <form id="delete-form-{{ $item->id }}" 
                                                      action="{{ route('admin.menu-items.destroy', $item) }}" 
                                                      method="POST" 
                                                      style="display: none;">
                                                    @csrf
                                                    @method('DELETE')
                                                </form>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $menuItems->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-utensils fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">ไม่พบเมนูอาหาร</h5>
                            @if(request()->has('search') || request()->has('category'))
                                <p class="text-muted">ลองเปลี่ยนเงื่อนไขการค้นหา</p>
                                <a href="{{ route('admin.menu-items.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>ล้างการค้นหา
                                </a>
                            @else
                                <p class="text-muted">เริ่มต้นสร้างเมนูแรกของคุณ</p>
                                <a href="{{ route('admin.menu-items.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>เพิ่มเมนูแรก
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function confirmDelete(itemId, itemName) {
    Swal.fire({
        title: 'ยืนยันการลบ',
        text: `คุณต้องการลบเมนู "${itemName}" หรือไม่?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'ลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById('delete-form-' + itemId).submit();
        }
    });
}
</script>
@endpush
@endsection

@push('styles')
<style>
/* Back Button Styles */
.btn-back {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.btn-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-back:hover::before {
    left: 100%;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B4513;
    color: #8B4513;
    background: #f8f9fa;
}

.btn-back i {
    transition: transform 0.3s ease;
}

.btn-back:hover i {
    transform: translateX(-3px);
}

/* Add Button Styles */
.btn-add {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
    background: linear-gradient(135deg, #A0522D 0%, #CD853F 100%);
}

.btn-add i {
    transition: transform 0.3s ease;
}

.btn-add:hover i {
    transform: rotate(90deg);
}
</style>
@endpush
