<?php $__env->startSection('title', 'จัดการข่าวสาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่หน้าหลัก
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary fade-in-up">
                        <i class="fas fa-newspaper me-2"></i>จัดการข่าวสาร
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active">ข่าวสาร</li>
                        </ol>
                    </nav>
                </div>
                <a href="<?php echo e(route('admin.news.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มข่าวสารใหม่
                </a>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- News List -->
    <div class="card border-0 shadow-lg">
        <div class="card-header">
            <h5 class="mb-0 text-white">
                <i class="fas fa-list me-2"></i>รายการข่าวสาร
            </h5>
        </div>
        <div class="card-body p-0">
            <?php if($news->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="60">#</th>
                                <th>หัวข้อ</th>
                                <th width="120">สถานะ</th>
                                <th width="120">แนะนำ</th>
                                <th width="150">วันที่เผยแพร่</th>
                                <th width="120">ผู้สร้าง</th>
                                <th width="150">จัดการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($item->sort_order ?: $item->id); ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($item->image): ?>
                                                <img src="<?php echo e(asset('storage/' . $item->image)); ?>"
                                                     alt="<?php echo e($item->title); ?>"
                                                     class="rounded me-3"
                                                     style="width: 50px; height: 50px; object-fit: cover;"
                                                     loading="lazy"
                                                     onerror="this.onerror=null; this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; display: none;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            <?php else: ?>
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <h6 class="mb-1"><?php echo e($item->title); ?></h6>
                                                <small class="text-muted"><?php echo e($item->short_content); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($item->is_published): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-eye me-1"></i>เผยแพร่
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-eye-slash me-1"></i>ร่าง
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($item->is_featured): ?>
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-star me-1"></i>แนะนำ
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-light text-dark">
                                                <i class="far fa-star me-1"></i>ทั่วไป
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo e($item->formatted_published_at); ?>

                                        </small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo e($item->creator->name); ?>

                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="<?php echo e(route('admin.news.show', $item)); ?>" 
                                               class="btn btn-outline-info" 
                                               title="ดู">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.news.edit', $item)); ?>" 
                                               class="btn btn-outline-warning" 
                                               title="แก้ไข">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="<?php echo e(route('admin.news.destroy', $item)); ?>" 
                                                  method="POST" 
                                                  class="d-inline"
                                                  onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบข่าวสารนี้?')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" 
                                                        class="btn btn-outline-danger" 
                                                        title="ลบ">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($news->hasPages()): ?>
                    <div class="card-footer bg-light">
                        <?php echo e($news->links()); ?>

                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">ยังไม่มีข่าวสาร</h5>
                    <p class="text-muted">เริ่มต้นสร้างข่าวสารแรกของคุณ</p>
                    <a href="<?php echo e(route('admin.news.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มข่าวสารใหม่
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Back Button Styles */
.btn-back {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.btn-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-back:hover::before {
    left: 100%;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B4513;
    color: #8B4513;
    background: #f8f9fa;
}

.btn-back i {
    transition: transform 0.3s ease;
}

.btn-back:hover i {
    transform: translateX(-3px);
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/admin/news/index.blade.php ENDPATH**/ ?>